generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int      @id @default(autoincrement())
  uid       String   @unique @default(uuid())
  username  String   @unique
  email     String   @unique
  password  String
  salt      String
  verified  <PERSON>olean  @default(false)
  active    Boolean  @default(true)
  role      String   @default("user")
  profileId Int?     @unique
  profile   Profile?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Profile {
  id        Int      @id @default(autoincrement())
  userId    String   @unique
  bio       String?
  image     String?
  user      User     @relation(fields: [userId], references: [uid])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Article {
  id        Int       @id @default(autoincrement())
  title     String
  content   String
  published Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Task {
  id        Int       @id @default(autoincrement())
  title     String
  completed Boolean   @default(false)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}