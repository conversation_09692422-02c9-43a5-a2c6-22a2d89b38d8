<html
  lang="en"
  xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>{{subject}}</title>
    <style>
      /* General resets */
      body,
      table,
      td,
      a {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }
      table,
      td {
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
      }
      img {
        -ms-interpolation-mode: bicubic;
        border: 0;
        outline: none;
        text-decoration: none;
      }
      body {
        margin: 0;
        padding: 0;
        width: 100% !important;
        height: 100% !important;
      }
      /* Container */
      .wrapper {
        width: 100%;
        background-color: #f6f7fb;
        padding: 24px 0;
      }
      .container {
        width: 100%;
        max-width: 600px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
      }
      .header {
        padding: 24px;
        background: #0f172a;
        color: #ffffff;
        font-family: Arial, Helvetica, sans-serif;
      }
      .brand {
        font-size: 18px;
        font-weight: 700;
        letter-spacing: 0.3px;
      }
      .content {
        padding: 24px;
        font-family: Arial, Helvetica, sans-serif;
        color: #0f172a;
        line-height: 1.6;
        font-size: 15px;
      }
      .footer {
        padding: 16px 24px 24px;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 12px;
        color: #64748b;
      }
      .divider {
        border-top: 1px solid #e2e8f0;
        margin: 24px 0;
      }
      .muted {
        color: #64748b;
      }
      .center {
        text-align: center;
      }

      /* Button fallback styles (non-Outlook) */
      .btn {
        display: inline-block;
        padding: 12px 20px;
        border-radius: 10px;
        text-decoration: none;
        font-weight: 700;
        font-family: Arial, Helvetica, sans-serif;
      }
      .btn-primary {
        background-color: #2563eb;
        color: #ffffff !important;
      }

      /* Dark mode (supported clients) */
      @media (prefers-color-scheme: dark) {
        .wrapper {
          background: #0b1020 !important;
        }
        .container {
          background: #111827 !important;
        }
        .header {
          background: #0b1020 !important;
        }
        .content {
          color: #e5e7eb !important;
        }
        .footer {
          color: #9ca3af !important;
        }
        .divider {
          border-top-color: #1f2937 !important;
        }
      }

      /* Mobile */
      @media screen and (max-width: 600px) {
        .content,
        .header,
        .footer {
          padding: 20px !important;
        }
      }
    </style>
  </head>
  <body>
    <div class="wrapper">
      <table
        role="presentation"
        width="100%"
        border="0"
        cellspacing="0"
        cellpadding="0"
      >
        <tr>
          <td align="center">
            <table
              role="presentation"
              class="container"
              border="0"
              cellspacing="0"
              cellpadding="0"
            >
              <tr>
                <td class="header">
                  <div class="brand">{{#if appName}}{{appName}}{{else}}Your App{{/if}}</div>
                </td>
              </tr>
              <tr>
                <td class="content">
                  {{{body}}}
                </td>
              </tr>
              <tr>
                <td class="footer">
                  <div class="divider"></div>
                  <div class="muted">
                    You’re receiving this email because you signed up for
                    {{#if appName}}{{appName}}{{else}}our service{{/if}}. If
                    this wasn’t you, please ignore this message or contact us at
                    {{#if
                      supportEmail
                    }}{{supportEmail}}{{else}}<EMAIL>{{/if}}.
                  </div>
                  <div style="height: 10px"></div>
                  <div class="muted">©
                    {{year}}
                    {{#if appName}}{{appName}}{{else}}Your Company{{/if}}. All
                    rights reserved.</div>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </div>
  </body>
</html>