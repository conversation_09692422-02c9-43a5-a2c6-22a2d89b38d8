export const HttpRes = Object.freeze({
  status: {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    REDIRECT: 302,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    INTERNAL_SERVER_ERROR: 500,
  },
  message: {
    OK: "OK",
    CREATED: "Created",
    UPDATED: "Updated",
    DELETED: "Deleted",
    NO_CONTENT: "No Content",
    BAD_REQUEST: "Bad Request",
    UNAUTHORIZED: "Unauthorized",
    FORBIDDEN: "Forbidden",
    NOT_FOUND: "Not Found",
    CONFLICT: "Conflict",
    INTERNAL_SERVER_ERROR: "Internal Server Error",
  },
  details: {
    OK: "Operation completed successfully",
    CREATED: "Resource created successfully",
    UPDATE: "Resource updated successfully",
    DELETE: "Resource deleted successfully",
    NO_CONTENT: "No Content",
    BAD_REQUEST: "The request was invalid or cannot be served",
    UNAUTHORIZED: "You are not authorized to access this resource",
    FOR<PERSON><PERSON><PERSON>N: "You do not have permission to perform this action",
    NOT_FOUND: "The requested resource was not found",
    CONFLICT: "A conflict occurred with the current state of the resource",
    INTERNAL_SERVER_ERROR: "An internal server error occurred",
  },
});
