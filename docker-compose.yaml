# database postgres
services:
  postgres:
    image: postgres:17-alpine
    container_name: postgres-db
    restart: unless-stopped
    ports:
      - "5432:5432"
    env_file:
      - .env.prod
    volumes:
      - postgres_data:/var/lib/postgresql/data
  redis:
    image: redis:8-alpine
    container_name: redis-db
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
  app:
    build: .
    container_name: app
    restart: unless-stopped
    ports:
      - "2000:2000"
    env_file:
      - .env.prod
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - JWT_SECRET=${JWT_SECRET}
      - GMAIL_APP_PASSWORD=${GMAIL_APP_PASSWORD}
      - GMAIL_USER=${GMAIL_USER}
      - CLOUD_NAME=${CLOUD_NAME}
      - CLOUD_API_KEY=${CLOUD_API_KEY}
      - CLOUD_API_SECRET=${CLOUD_API_SECRET}
    depends_on:
      - postgres
      - redis
volumes:
  postgres_data:
  redis_data:
