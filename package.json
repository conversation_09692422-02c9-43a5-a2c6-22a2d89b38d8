{"name": "backend", "version": "1.0.0", "main": "src/server.ts", "prisma": {"seed": "tsx prisma/seed.ts"}, "scripts": {"build": "tsc && tsc-alias", "start": "node dist/server.js", "dev": "nodemon", "preview": "tsc && tsc-alias && node dist/server.js", "lint": "eslint 'src/**/*.ts'"}, "keywords": [], "author": "purwa<PERSON>ka", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.14.0", "body-parser": "^2.2.0", "cloudinary": "^2.7.0", "dotenv": "^17.2.1", "express": "^5.1.0", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "nodemailer": "^7.0.5", "nodemailer-express-handlebars": "^6.1.2", "playwright": "^1.55.0", "prisma": "^6.14.0", "winston": "^3.17.0", "yup": "^1.7.0"}, "devDependencies": {"@playwright/test": "^1.55.0", "@types/express": "^5.0.3", "@types/node": "^24.2.1", "@types/nodemailer-express-handlebars": "^4.0.5", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "eslint": "^9.33.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.4", "typescript": "^5.9.2"}}